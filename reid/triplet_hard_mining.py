#!/usr/bin/env python3
"""
Triplet Loss + Hard Mining - 专门针对个体识别的三元组损失和困难样本挖掘
这种方法在人脸识别和ReID任务中非常有效
"""

import os
import sys
import json
import random
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
from collections import defaultdict

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torch.nn.functional as F
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from feature_based_cat_recognition import FeatureExtractorModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TripletModel(nn.Module):
    """三元组模型 - 专门为个体识别设计"""
    
    def __init__(self, base_model, feature_dim=2048, embedding_dim=512):
        super(TripletModel, self).__init__()
        self.feature_extractor = base_model
        self.feature_dim = feature_dim
        self.embedding_dim = embedding_dim
        
        # 解冻更多层
        for param in self.feature_extractor.backbone.parameters():
            param.requires_grad = False
        
        # 解冻最后两个stage
        for param in self.feature_extractor.backbone.layers[-2:].parameters():
            param.requires_grad = True
        for param in self.feature_extractor.backbone.norm.parameters():
            param.requires_grad = True
        
        # 嵌入层 - 将特征映射到更低维的嵌入空间
        self.embedding_layer = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Linear(feature_dim, embedding_dim),
            nn.BatchNorm1d(embedding_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            
            nn.Linear(embedding_dim, embedding_dim)
        )
        
        # 分类器（用于辅助训练）
        self.classifier = nn.Sequential(
            nn.Linear(embedding_dim, embedding_dim),
            nn.BatchNorm1d(embedding_dim),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(embedding_dim, 33)  # 假设33个类别
        )
    
    def forward(self, x, return_embeddings=False):
        # 特征提取
        features = self.feature_extractor(x)
        
        # 嵌入
        embeddings = self.embedding_layer(features)
        embeddings = F.normalize(embeddings, p=2, dim=1)  # L2归一化
        
        if return_embeddings:
            return embeddings
        
        # 分类
        logits = self.classifier(embeddings)
        
        return logits, embeddings

class TripletDataset(Dataset):
    """三元组数据集"""
    
    def __init__(self, original_annotations: Dict, individual_data_dir: str, 
                 original_dir: str, cat_to_id: Dict[str, int], 
                 max_individual_cats: int = 30, is_training=True):
        
        self.original_dir = original_dir
        self.individual_data_dir = individual_data_dir
        self.cat_to_id = cat_to_id
        self.is_training = is_training
        
        # 数据增强
        if is_training:
            self.transform = transforms.Compose([
                transforms.Resize((256, 256)),
                transforms.RandomCrop((224, 224)),
                transforms.RandomHorizontalFlip(0.5),
                transforms.RandomRotation(15),
                transforms.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.3, hue=0.15),
                transforms.RandomAffine(degrees=0, translate=(0.1, 0.1), scale=(0.9, 1.1)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                transforms.RandomErasing(p=0.2, scale=(0.02, 0.1))
            ])
        else:
            self.transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        
        # 按类别组织数据
        self.samples_by_class = defaultdict(list)
        
        # 处理原始3猫数据
        for filename, annotation in original_annotations.items():
            if annotation['category'] in ['小白', '小花', '小黑']:
                self.samples_by_class[cat_to_id[annotation['category']]].append({
                    'image_path': os.path.join(original_dir, filename),
                    'category': annotation['category'],
                    'label': cat_to_id[annotation['category']],
                    'is_original': True
                })
        
        # 处理个体猫咪数据
        if os.path.exists(individual_data_dir):
            individual_folders = sorted([f for f in os.listdir(individual_data_dir) 
                                       if os.path.isdir(os.path.join(individual_data_dir, f)) 
                                       and f.isdigit()])
            
            if max_individual_cats:
                individual_folders = individual_folders[:max_individual_cats]
            
            for cat_folder in individual_folders:
                cat_name = f"individual_{cat_folder}"
                
                if cat_name not in cat_to_id:
                    cat_to_id[cat_name] = len(cat_to_id)
                
                folder_path = os.path.join(individual_data_dir, cat_folder)
                for img_file in os.listdir(folder_path):
                    if img_file.lower().endswith(('.jpg', '.jpeg', '.png')):
                        self.samples_by_class[cat_to_id[cat_name]].append({
                            'image_path': os.path.join(folder_path, img_file),
                            'category': cat_name,
                            'label': cat_to_id[cat_name],
                            'is_original': False
                        })
        
        # 创建所有样本的列表
        self.all_samples = []
        for class_id, samples in self.samples_by_class.items():
            self.all_samples.extend(samples)
        
        self.classes = list(self.samples_by_class.keys())
        
        logger.info(f"三元组数据集: {len(self.classes)} 个类别, {len(self.all_samples)} 个样本")
    
    def __len__(self):
        return len(self.all_samples)
    
    def __getitem__(self, idx):
        # 获取anchor样本
        anchor_sample = self.all_samples[idx]
        anchor_class = anchor_sample['label']
        
        # 选择positive样本（同类别）
        positive_candidates = self.samples_by_class[anchor_class]
        positive_sample = random.choice(positive_candidates)
        while positive_sample['image_path'] == anchor_sample['image_path']:
            positive_sample = random.choice(positive_candidates)
        
        # 选择negative样本（不同类别）
        negative_class = random.choice([c for c in self.classes if c != anchor_class])
        negative_sample = random.choice(self.samples_by_class[negative_class])
        
        # 加载图像
        def load_image(sample):
            try:
                image = Image.open(sample['image_path']).convert('RGB')
                return self.transform(image)
            except Exception as e:
                logger.error(f"无法加载图像 {sample['image_path']}: {e}")
                return torch.randn(3, 224, 224)
        
        anchor_img = load_image(anchor_sample)
        positive_img = load_image(positive_sample)
        negative_img = load_image(negative_sample)
        
        return (anchor_img, positive_img, negative_img, 
                anchor_class, anchor_sample['is_original'])

class TripletLoss(nn.Module):
    """三元组损失"""
    
    def __init__(self, margin=0.5):
        super(TripletLoss, self).__init__()
        self.margin = margin
    
    def forward(self, anchor, positive, negative):
        # 计算距离
        pos_dist = F.pairwise_distance(anchor, positive, p=2)
        neg_dist = F.pairwise_distance(anchor, negative, p=2)
        
        # 三元组损失
        loss = F.relu(pos_dist - neg_dist + self.margin)
        
        return loss.mean()

class HardTripletLoss(nn.Module):
    """困难三元组损失 - 在线困难样本挖掘"""
    
    def __init__(self, margin=0.5):
        super(HardTripletLoss, self).__init__()
        self.margin = margin
    
    def forward(self, embeddings, labels):
        # 计算所有样本之间的距离矩阵
        dist_matrix = torch.cdist(embeddings, embeddings, p=2)
        
        batch_size = embeddings.size(0)
        loss = 0.0
        valid_triplets = 0
        
        for i in range(batch_size):
            anchor_label = labels[i]
            
            # 找到所有positive样本（同类别，但不是自己）
            positive_mask = (labels == anchor_label) & (torch.arange(batch_size).to(labels.device) != i)
            
            # 找到所有negative样本（不同类别）
            negative_mask = labels != anchor_label
            
            if positive_mask.sum() == 0 or negative_mask.sum() == 0:
                continue
            
            # 困难positive：距离最远的positive样本
            positive_dists = dist_matrix[i][positive_mask]
            hard_positive_dist = positive_dists.max()
            
            # 困难negative：距离最近的negative样本
            negative_dists = dist_matrix[i][negative_mask]
            hard_negative_dist = negative_dists.min()
            
            # 计算三元组损失
            triplet_loss = F.relu(hard_positive_dist - hard_negative_dist + self.margin)
            loss += triplet_loss
            valid_triplets += 1
        
        if valid_triplets > 0:
            loss = loss / valid_triplets
        
        return loss

def triplet_train_epoch(model, dataloader, optimizer, triplet_criterion, classification_criterion, 
                       device, epoch, use_hard_mining=True):
    """三元组训练一个epoch"""
    
    model.train()
    
    total_loss = 0.0
    triplet_loss_sum = 0.0
    classification_loss_sum = 0.0
    num_batches = 0
    
    for batch_data in dataloader:
        if use_hard_mining:
            # 使用困难样本挖掘
            images, labels, is_original = batch_data
            images = images.to(device)
            labels = labels.to(device)
            is_original = is_original.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            logits, embeddings = model(images)
            
            # 困难三元组损失
            triplet_loss = triplet_criterion(embeddings, labels)
            
            # 分类损失
            classification_loss = classification_criterion(logits, labels)
            
            # 对原始3猫数据给予更高权重
            weights = torch.where(is_original, 2.0, 1.0).mean()
            classification_loss = classification_loss * weights
            
        else:
            # 使用传统三元组
            anchor_imgs, positive_imgs, negative_imgs, labels, is_original = batch_data
            
            anchor_imgs = anchor_imgs.to(device)
            positive_imgs = positive_imgs.to(device)
            negative_imgs = negative_imgs.to(device)
            labels = labels.to(device)
            is_original = is_original.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            anchor_logits, anchor_embeddings = model(anchor_imgs)
            _, positive_embeddings = model(positive_imgs)
            _, negative_embeddings = model(negative_imgs)
            
            # 三元组损失
            triplet_loss = triplet_criterion(anchor_embeddings, positive_embeddings, negative_embeddings)
            
            # 分类损失
            classification_loss = classification_criterion(anchor_logits, labels)
            
            # 对原始3猫数据给予更高权重
            weights = torch.where(is_original, 2.0, 1.0).mean()
            classification_loss = classification_loss * weights
        
        # 总损失
        total_loss_batch = triplet_loss + 0.5 * classification_loss
        
        total_loss_batch.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        total_loss += total_loss_batch.item()
        triplet_loss_sum += triplet_loss.item()
        classification_loss_sum += classification_loss.item()
        num_batches += 1
    
    avg_total_loss = total_loss / num_batches
    avg_triplet_loss = triplet_loss_sum / num_batches
    avg_classification_loss = classification_loss_sum / num_batches
    
    logger.info(f"Triplet训练 Epoch {epoch+1}")
    logger.info(f"  总损失: {avg_total_loss:.4f}")
    logger.info(f"  三元组损失: {avg_triplet_loss:.4f}")
    logger.info(f"  分类损失: {avg_classification_loss:.4f}")

def main():
    parser = argparse.ArgumentParser(description='Triplet Loss + Hard Mining')
    parser.add_argument('--annotations', required=True, help='标注文件')
    parser.add_argument('--individual-data-dir', required=True, help='个体数据目录')
    parser.add_argument('--original-dir', required=True, help='原始图像目录')
    parser.add_argument('--pretrained', required=True, help='预训练模型')
    parser.add_argument('--output', default='triplet_model.pth', help='输出模型')
    parser.add_argument('--max-individual-cats', type=int, default=30, help='最大个体猫咪数量')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--batch-size', type=int, default=16, help='批次大小')
    parser.add_argument('--lr', type=float, default=1e-4, help='学习率')
    parser.add_argument('--margin', type=float, default=0.5, help='三元组损失边界')
    parser.add_argument('--use-hard-mining', action='store_true', help='使用困难样本挖掘')
    
    args = parser.parse_args()
    
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 数据准备
    with open(args.annotations, 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    cat_to_id = {'小白': 0, '小花': 1, '小黑': 2}
    
    # 创建三元组数据集
    dataset = TripletDataset(
        annotations, args.individual_data_dir, args.original_dir, 
        cat_to_id, args.max_individual_cats, True
    )
    
    if args.use_hard_mining:
        # 困难样本挖掘需要不同的数据加载方式
        from enhanced_progressive_training import EnhancedProgressiveDataset
        dataset = EnhancedProgressiveDataset(
            annotations, args.individual_data_dir, args.original_dir, 
            cat_to_id, args.max_individual_cats, True
        )
        
        # 数据分割
        samples_by_cat = defaultdict(list)
        for i, sample in enumerate(dataset.samples):
            samples_by_cat[sample['label']].append(i)
        
        train_indices = []
        for cat_id, indices in samples_by_cat.items():
            random.shuffle(indices)
            train_indices.extend(indices)
        
        train_dataset = torch.utils.data.Subset(dataset, train_indices)
        dataloader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=2)
    else:
        dataloader = DataLoader(dataset, batch_size=args.batch_size, shuffle=True, num_workers=2)
    
    # 加载预训练模型
    checkpoint = torch.load(args.pretrained, map_location=device, weights_only=False)
    feature_dim = checkpoint.get('feature_dim', 2048)
    
    # 创建基础特征提取器
    if 'feature_extractor.backbone.patch_embed.proj.weight' in checkpoint['model_state_dict']:
        logger.info("从对比学习模型中提取特征提取器...")
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_state_dict = {}
        for key, value in checkpoint['model_state_dict'].items():
            if key.startswith('feature_extractor.'):
                new_key = key.replace('feature_extractor.', '')
                base_state_dict[new_key] = value
        base_model.load_state_dict(base_state_dict)
    else:
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_model.load_state_dict(checkpoint['model_state_dict'])
    
    # 创建三元组模型
    model = TripletModel(base_model, feature_dim, embedding_dim=512)
    model = model.to(device)
    
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"可训练参数: {trainable_params:,} / {total_params:,}")
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.epochs)
    
    if args.use_hard_mining:
        triplet_criterion = HardTripletLoss(margin=args.margin)
    else:
        triplet_criterion = TripletLoss(margin=args.margin)
    
    classification_criterion = nn.CrossEntropyLoss()
    
    # 训练
    logger.info(f"开始Triplet训练 (困难样本挖掘: {args.use_hard_mining})...")
    
    for epoch in range(args.epochs):
        triplet_train_epoch(
            model, dataloader, optimizer, triplet_criterion, classification_criterion,
            device, epoch, args.use_hard_mining
        )
        
        scheduler.step()
        
        # 每20轮保存一次
        if (epoch + 1) % 20 == 0:
            torch.save({
                'model_state_dict': model.state_dict(),
                'feature_dim': feature_dim,
                'embedding_dim': 512,
                'epoch': epoch
            }, f'triplet_model_epoch_{epoch+1}.pth')
    
    # 保存最终模型
    torch.save({
        'model_state_dict': model.state_dict(),
        'feature_dim': feature_dim,
        'embedding_dim': 512,
        'cat_to_id': cat_to_id
    }, args.output)
    
    logger.info(f"Triplet模型已保存到: {args.output}")

if __name__ == "__main__":
    main()
